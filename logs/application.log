2025-08-08 10:54:28.013 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-08 10:54:28.309 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-jfx
2025-08-08 10:54:28.503 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-08 10:54:28.927 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-08 10:54:28.928 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-08 10:54:28.929 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-08 10:54:29.354 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-08 10:54:31.855 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 10:54:31.855 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 10:54:31.855 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 10:54:41.273 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-08 10:54:41.273 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-08 10:54:45.622 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-08 10:54:45.623 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-08 10:56:21.808 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-08 10:56:22.053 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-jfx
2025-08-08 10:56:22.288 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-08 10:56:22.728 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-08 10:56:22.729 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-08 10:56:22.730 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-08 10:56:23.257 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-08 10:56:24.983 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 10:56:24.983 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 10:56:24.984 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 10:56:29.934 [JavaFX Application Thread] INFO  c.l.controller.MainController - 返回主页面按钮Action事件被触发
2025-08-08 10:56:29.934 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-08 10:56:30.999 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 10:56:30.999 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 10:56:30.999 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 10:56:42.988 [JavaFX Application Thread] INFO  c.l.controller.MainController - 返回主页面按钮Action事件被触发
2025-08-08 10:56:42.988 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-08 10:56:45.935 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-08 10:56:45.935 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-08 10:56:47.430 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-08 10:56:47.431 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-08 11:26:39.615 [main] INFO  com.logictrue.DataDetailTest - 开始数据详情功能测试
2025-08-08 11:26:39.819 [main] INFO  c.logictrue.service.DatabaseService - 数据表创建成功: data_records
2025-08-08 11:26:39.821 [main] INFO  c.logictrue.service.DatabaseService - 数据库初始化成功，数据库路径: /home/<USER>/nl-mes/iot-jfx/data_records.db
2025-08-08 11:26:39.821 [main] INFO  com.logictrue.DataDetailTest - 开始插入测试数据
2025-08-08 11:26:39.848 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: sensor_data_001.json
2025-08-08 11:26:39.878 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: temperature_log_002.csv
2025-08-08 11:26:39.907 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: pressure_reading_003.xml
2025-08-08 11:26:39.936 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: humidity_data_004.json
2025-08-08 11:26:39.965 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: vibration_log_005.csv
2025-08-08 11:26:39.994 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: flow_rate_006.xml
2025-08-08 11:26:40.024 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: voltage_reading_007.json
2025-08-08 11:26:40.053 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: current_data_008.csv
2025-08-08 11:26:40.088 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: power_log_009.xml
2025-08-08 11:26:40.117 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: speed_sensor_010.json
2025-08-08 11:26:40.147 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: position_data_011.csv
2025-08-08 11:26:40.176 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: acceleration_012.xml
2025-08-08 11:26:40.206 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: gyroscope_013.json
2025-08-08 11:26:40.235 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: magnetometer_014.csv
2025-08-08 11:26:40.265 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: barometer_015.xml
2025-08-08 11:26:40.297 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: light_sensor_016.json
2025-08-08 11:26:40.326 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: proximity_017.csv
2025-08-08 11:26:40.356 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: sound_level_018.xml
2025-08-08 11:26:40.385 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: gas_sensor_019.json
2025-08-08 11:26:40.416 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: ph_value_020.csv
2025-08-08 11:26:40.458 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: conductivity_021.xml
2025-08-08 11:26:40.491 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: turbidity_022.json
2025-08-08 11:26:40.521 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: dissolved_oxygen_023.csv
2025-08-08 11:26:40.556 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: chlorine_024.xml
2025-08-08 11:26:40.586 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: nitrogen_025.json
2025-08-08 11:26:40.615 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: phosphorus_026.csv
2025-08-08 11:26:40.646 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: potassium_027.xml
2025-08-08 11:26:40.675 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: calcium_028.json
2025-08-08 11:26:40.711 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: magnesium_029.csv
2025-08-08 11:26:40.745 [main] INFO  c.logictrue.service.DatabaseService - 插入数据记录成功: sulfur_030.xml
2025-08-08 11:26:40.755 [main] INFO  com.logictrue.DataDetailTest - 测试数据插入完成，成功插入 30 条记录
2025-08-08 11:26:40.755 [main] INFO  com.logictrue.DataDetailTest - 开始测试分页查询功能
2025-08-08 11:26:40.759 [main] INFO  com.logictrue.DataDetailTest - 数据库总记录数: 30
2025-08-08 11:26:40.759 [main] INFO  com.logictrue.DataDetailTest - 测试页面大小: 5
2025-08-08 11:26:40.759 [main] INFO  com.logictrue.DataDetailTest - 总页数: 6
2025-08-08 11:26:40.772 [main] INFO  com.logictrue.DataDetailTest - 第 1 页查询结果: 5 条记录
2025-08-08 11:26:40.773 [main] INFO  com.logictrue.DataDetailTest -   记录 1: ID=23, 文件名=dissolved_oxygen_023.csv, 采集时间=2025-08-08 08:13:39, 路径=/logs/position/dissolved_oxygen_023.csv
2025-08-08 11:26:40.773 [main] INFO  com.logictrue.DataDetailTest -   记录 2: ID=3, 文件名=pressure_reading_003.xml, 采集时间=2025-08-05 23:50:39, 路径=/readings/pressure/pressure_reading_003.xml
2025-08-08 11:26:40.774 [main] INFO  com.logictrue.DataDetailTest - 第 2 页查询结果: 5 条记录
2025-08-08 11:26:40.774 [main] INFO  com.logictrue.DataDetailTest -   记录 1: ID=24, 文件名=chlorine_024.xml, 采集时间=2025-08-02 15:31:39, 路径=/readings/acceleration/chlorine_024.xml
2025-08-08 11:26:40.774 [main] INFO  com.logictrue.DataDetailTest -   记录 2: ID=29, 文件名=magnesium_029.csv, 采集时间=2025-08-02 01:16:39, 路径=/logs/vibration/magnesium_029.csv
2025-08-08 11:26:40.775 [main] INFO  com.logictrue.DataDetailTest - 第 3 页查询结果: 5 条记录
2025-08-08 11:26:40.776 [main] INFO  com.logictrue.DataDetailTest -   记录 1: ID=2, 文件名=temperature_log_002.csv, 采集时间=2025-07-25 14:52:39, 路径=/logs/temperature/temperature_log_002.csv
2025-08-08 11:26:40.776 [main] INFO  com.logictrue.DataDetailTest -   记录 2: ID=4, 文件名=humidity_data_004.json, 采集时间=2025-07-25 02:04:39, 路径=/data/humidity/humidity_data_004.json
2025-08-08 11:26:40.776 [main] INFO  com.logictrue.DataDetailTest - 测试页面大小: 10
2025-08-08 11:26:40.776 [main] INFO  com.logictrue.DataDetailTest - 总页数: 3
2025-08-08 11:26:40.777 [main] INFO  com.logictrue.DataDetailTest - 第 1 页查询结果: 10 条记录
2025-08-08 11:26:40.778 [main] INFO  com.logictrue.DataDetailTest -   记录 1: ID=23, 文件名=dissolved_oxygen_023.csv, 采集时间=2025-08-08 08:13:39, 路径=/logs/position/dissolved_oxygen_023.csv
2025-08-08 11:26:40.778 [main] INFO  com.logictrue.DataDetailTest -   记录 2: ID=3, 文件名=pressure_reading_003.xml, 采集时间=2025-08-05 23:50:39, 路径=/readings/pressure/pressure_reading_003.xml
2025-08-08 11:26:40.779 [main] INFO  com.logictrue.DataDetailTest - 第 2 页查询结果: 10 条记录
2025-08-08 11:26:40.780 [main] INFO  com.logictrue.DataDetailTest -   记录 1: ID=2, 文件名=temperature_log_002.csv, 采集时间=2025-07-25 14:52:39, 路径=/logs/temperature/temperature_log_002.csv
2025-08-08 11:26:40.780 [main] INFO  com.logictrue.DataDetailTest -   记录 2: ID=4, 文件名=humidity_data_004.json, 采集时间=2025-07-25 02:04:39, 路径=/data/humidity/humidity_data_004.json
2025-08-08 11:26:40.781 [main] INFO  com.logictrue.DataDetailTest - 第 3 页查询结果: 10 条记录
2025-08-08 11:26:40.781 [main] INFO  com.logictrue.DataDetailTest -   记录 1: ID=18, 文件名=sound_level_018.xml, 采集时间=2025-07-19 13:02:39, 路径=/readings/flow/sound_level_018.xml
2025-08-08 11:26:40.782 [main] INFO  com.logictrue.DataDetailTest -   记录 2: ID=22, 文件名=turbidity_022.json, 采集时间=2025-07-17 23:25:39, 路径=/data/motion/turbidity_022.json
2025-08-08 11:26:40.782 [main] INFO  com.logictrue.DataDetailTest - 测试页面大小: 20
2025-08-08 11:26:40.782 [main] INFO  com.logictrue.DataDetailTest - 总页数: 2
2025-08-08 11:26:40.784 [main] INFO  com.logictrue.DataDetailTest - 第 1 页查询结果: 20 条记录
2025-08-08 11:26:40.785 [main] INFO  com.logictrue.DataDetailTest -   记录 1: ID=23, 文件名=dissolved_oxygen_023.csv, 采集时间=2025-08-08 08:13:39, 路径=/logs/position/dissolved_oxygen_023.csv
2025-08-08 11:26:40.785 [main] INFO  com.logictrue.DataDetailTest -   记录 2: ID=3, 文件名=pressure_reading_003.xml, 采集时间=2025-08-05 23:50:39, 路径=/readings/pressure/pressure_reading_003.xml
2025-08-08 11:26:40.787 [main] INFO  com.logictrue.DataDetailTest - 第 2 页查询结果: 10 条记录
2025-08-08 11:26:40.787 [main] INFO  com.logictrue.DataDetailTest -   记录 1: ID=18, 文件名=sound_level_018.xml, 采集时间=2025-07-19 13:02:39, 路径=/readings/flow/sound_level_018.xml
2025-08-08 11:26:40.787 [main] INFO  com.logictrue.DataDetailTest -   记录 2: ID=22, 文件名=turbidity_022.json, 采集时间=2025-07-17 23:25:39, 路径=/data/motion/turbidity_022.json
2025-08-08 11:26:40.787 [main] INFO  com.logictrue.DataDetailTest - 分页查询功能测试完成
2025-08-08 11:26:40.788 [main] INFO  com.logictrue.DataDetailTest - 数据详情功能测试完成
2025-08-08 11:26:49.840 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-08 11:26:50.081 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-jfx
2025-08-08 11:26:50.255 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-08 11:26:50.632 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-08 11:26:50.633 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-08 11:26:50.634 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-08 11:26:51.011 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-08 11:26:52.726 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 11:26:52.726 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 11:26:52.727 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 11:26:54.808 [JavaFX Application Thread] INFO  c.l.controller.MainController - 数据详情按钮Action事件被触发
2025-08-08 11:26:54.808 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到数据详情页面
2025-08-08 11:26:54.891 [JavaFX Application Thread] ERROR c.l.controller.MainController - 加载数据详情页面失败
javafx.fxml.LoadException: FXCollections is not a valid type.
/home/<USER>/nl-mes/iot-jfx/build/resources/main/fxml/data-detail.fxml:49

	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.constructLoadException(FXMLLoader.java:2718)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.createElement(FXMLLoader.java:2920)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.processStartElement(FXMLLoader.java:2850)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2649)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.8/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.logictrue.controller.MainController.showDataDetailPage(MainController.java:430)
	at com.logictrue.controller.MainController.lambda$initializeUI$3(MainController.java:210)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:49)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Node.fireEvent(Node.java:8875)
	at javafx.controls@21.0.8/javafx.scene.control.Button.fire(Button.java:203)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.behavior.ButtonBehavior.mouseReleased(ButtonBehavior.java:207)
	at javafx.controls@21.0.8/com.sun.javafx.scene.control.inputmap.InputMap.handle(InputMap.java:274)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler$NormalEventHandlerRecord.handleBubblingEvent(CompositeEventHandler.java:247)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:80)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.8/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.8/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.8/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.8/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:54)
	at javafx.base@21.0.8/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.8/javafx.scene.Scene$MouseHandler.process(Scene.java:3984)
	at javafx.graphics@21.0.8/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.8/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.8/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.8/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication._runLoop(Native Method)
	at javafx.graphics@21.0.8/com.sun.glass.ui.gtk.GtkApplication.lambda$runLoop$10(GtkApplication.java:263)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-08 11:27:08.012 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-08 11:27:08.012 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-08 11:27:09.463 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-08 11:27:09.464 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-08 11:30:33.717 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-08 11:30:33.986 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-jfx
2025-08-08 11:30:34.198 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-08 11:30:34.588 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-08 11:30:34.589 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-08 11:30:34.591 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-08 11:30:35.020 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-08 11:30:36.402 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 11:30:36.402 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 11:30:36.402 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 11:30:38.175 [JavaFX Application Thread] INFO  c.l.controller.MainController - 数据详情按钮Action事件被触发
2025-08-08 11:30:38.176 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到数据详情页面
2025-08-08 11:30:38.385 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据表创建成功: data_records
2025-08-08 11:30:38.385 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库初始化成功，数据库路径: /home/<USER>/nl-mes/iot-jfx/data_records.db
2025-08-08 11:30:38.391 [JavaFX Application Thread] INFO  c.l.controller.DataDetailController - 数据详情控制器初始化完成
2025-08-08 11:30:54.661 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-08 11:30:56.137 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 11:30:56.138 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 11:30:56.138 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 11:30:59.850 [JavaFX Application Thread] INFO  c.l.controller.MainController - 数据详情按钮Action事件被触发
2025-08-08 11:30:59.851 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到数据详情页面
2025-08-08 11:30:59.863 [JavaFX Application Thread] INFO  c.l.controller.DataDetailController - 数据详情控制器初始化完成
2025-08-08 11:31:03.327 [JavaFX Application Thread] INFO  c.l.controller.DataDetailController - 显示记录详情: DataRecord{id=23, fileName='dissolved_oxygen_023.csv', collectTime=2025-08-08T08:13:39.821, filePath='/logs/position/dissolved_oxygen_023.csv'}
2025-08-08 11:31:21.962 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-08 11:31:23.884 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-08 11:31:23.885 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-08 11:31:25.466 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-08 11:31:25.467 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-08 11:32:55.560 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-08 11:32:55.798 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-jfx
2025-08-08 11:32:55.975 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-08 11:32:56.318 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-08 11:32:56.319 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-08 11:32:56.320 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-08 11:32:56.640 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-08 11:32:57.858 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 11:32:57.859 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 11:32:57.859 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 11:32:58.672 [JavaFX Application Thread] INFO  c.l.controller.MainController - 数据详情按钮Action事件被触发
2025-08-08 11:32:58.673 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到数据详情页面
2025-08-08 11:32:58.866 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据表创建成功: data_records
2025-08-08 11:32:58.867 [JavaFX Application Thread] INFO  c.logictrue.service.DatabaseService - 数据库初始化成功，数据库路径: /home/<USER>/nl-mes/iot-jfx/data_records.db
2025-08-08 11:32:58.872 [JavaFX Application Thread] INFO  c.l.controller.DataDetailController - 数据详情控制器初始化完成
2025-08-08 11:33:04.790 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-08 11:33:05.883 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-08 11:33:05.883 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-08 11:33:07.233 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-08 11:33:07.237 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-08 14:52:39.922 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-08 14:52:40.201 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-jfx
2025-08-08 14:52:40.418 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-08 14:52:40.818 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-08 14:52:40.820 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-08 14:52:40.820 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-08 14:52:41.210 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-08 14:52:44.475 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 14:52:44.475 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 14:52:44.475 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 14:52:46.170 [JavaFX Application Thread] INFO  c.l.controller.MainController - 返回主页面按钮Action事件被触发
2025-08-08 14:52:46.170 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-08 14:52:47.170 [JavaFX Application Thread] INFO  c.l.controller.MainController - 设置按钮Action事件被触发
2025-08-08 14:52:47.171 [JavaFX Application Thread] INFO  c.l.controller.MainController - 开始打开设置窗口
2025-08-08 14:52:47.268 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表单字段表格初始化完成
2025-08-08 14:52:47.270 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 外部应用程序表格初始化完成
2025-08-08 14:52:47.270 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 配置文件管理初始化完成
2025-08-08 14:52:47.271 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 14:52:47.271 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 加载表单字段，共2个字段
2025-08-08 14:52:47.271 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=test1, label=测试字段1, name=test1, type=TEXT
2025-08-08 14:52:47.272 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=test2, label=测试字段2, name=test2, type=NUMBER
2025-08-08 14:52:47.272 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表单字段列表更新完成，当前列表大小: 2
2025-08-08 14:52:47.273 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 加载外部应用程序，共1个应用
2025-08-08 14:52:47.283 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - === 测试表单字段数据 ===
2025-08-08 14:52:47.283 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - formFieldsList大小: 2
2025-08-08 14:52:47.283 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[0]: id=test1, label=测试字段1, name=test1, type=文本框
2025-08-08 14:52:47.284 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[1]: id=test2, label=测试字段2, name=test2, type=数字框
2025-08-08 14:52:47.284 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - === 测试完成 ===
2025-08-08 14:52:47.284 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-08 14:52:47.701 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表格刷新完成
2025-08-08 14:53:07.362 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-08 14:53:07.363 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-08 14:53:08.898 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-08 14:53:08.899 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
2025-08-08 15:06:49.281 [JavaFX Application Thread] INFO  com.logictrue.App - 启动IoT数据采集应用程序
2025-08-08 15:06:49.554 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 应用程序目录 (开发模式): /home/<USER>/nl-mes/iot-jfx
2025-08-08 15:06:49.777 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - 配置文件加载成功
2025-08-08 15:06:50.163 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载默认背景图片成功
2025-08-08 15:06:50.164 [JavaFX Application Thread] INFO  c.l.controller.MainController - 加载外部应用程序按钮完成，共1个应用
2025-08-08 15:06:50.165 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面初始化完成
2025-08-08 15:06:50.599 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序启动成功
2025-08-08 15:06:52.866 [JavaFX Application Thread] INFO  c.l.controller.MainController - 快速开始按钮Action事件被触发
2025-08-08 15:06:52.867 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到表单页面
2025-08-08 15:06:52.867 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 15:06:55.162 [JavaFX Application Thread] INFO  c.l.controller.MainController - 返回主页面按钮Action事件被触发
2025-08-08 15:06:55.162 [JavaFX Application Thread] INFO  c.l.controller.MainController - 切换到主页面
2025-08-08 15:06:56.098 [JavaFX Application Thread] INFO  c.l.controller.MainController - 设置按钮Action事件被触发
2025-08-08 15:06:56.099 [JavaFX Application Thread] INFO  c.l.controller.MainController - 开始打开设置窗口
2025-08-08 15:06:56.214 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表单字段表格初始化完成
2025-08-08 15:06:56.216 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 外部应用程序表格初始化完成
2025-08-08 15:06:56.216 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 配置文件管理初始化完成
2025-08-08 15:06:56.217 [JavaFX Application Thread] INFO  com.logictrue.config.ConfigManager - ConfigManager获取表单字段，共2个
2025-08-08 15:06:56.217 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 加载表单字段，共2个字段
2025-08-08 15:06:56.217 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=test1, label=测试字段1, name=test1, type=TEXT
2025-08-08 15:06:56.217 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段信息: id=test2, label=测试字段2, name=test2, type=NUMBER
2025-08-08 15:06:56.218 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表单字段列表更新完成，当前列表大小: 2
2025-08-08 15:06:56.218 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 加载外部应用程序，共1个应用
2025-08-08 15:06:56.229 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - === 测试表单字段数据 ===
2025-08-08 15:06:56.229 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - formFieldsList大小: 2
2025-08-08 15:06:56.229 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[0]: id=test1, label=测试字段1, name=test1, type=文本框
2025-08-08 15:06:56.230 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 字段[1]: id=test2, label=测试字段2, name=test2, type=数字框
2025-08-08 15:06:56.230 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - === 测试完成 ===
2025-08-08 15:06:56.230 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 设置界面初始化完成
2025-08-08 15:06:56.679 [JavaFX Application Thread] INFO  c.l.controller.SettingsController - 表格刷新完成
2025-08-08 15:08:20.403 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏按钮Action事件被触发
2025-08-08 15:08:20.403 [JavaFX Application Thread] INFO  c.l.controller.MainController - 退出全屏模式
2025-08-08 15:08:22.065 [JavaFX Application Thread] INFO  com.logictrue.App - 应用程序正在关闭
2025-08-08 15:08:22.067 [JavaFX Application Thread] INFO  c.l.controller.MainController - 主界面控制器关闭
