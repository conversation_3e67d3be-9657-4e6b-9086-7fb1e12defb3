package com.logictrue.iot.entity;

/**
 * Excel模板单元格实体类
 */
public class ExcelTemplateCell {
    private String cellId;
    private String cellType;
    private Integer rowIndex;
    private Integer colIndex;
    private String fieldCode;
    private String cellValue;
    private String cellStyle;
    private String validation;
    private String formula;
    private Boolean required;
    private String description;

    // 默认构造函数
    public ExcelTemplateCell() {
    }

    // 带参数的构造函数
    public ExcelTemplateCell(String cellId, String cellType, Integer rowIndex, Integer colIndex, String fieldCode) {
        this.cellId = cellId;
        this.cellType = cellType;
        this.rowIndex = rowIndex;
        this.colIndex = colIndex;
        this.fieldCode = fieldCode;
    }

    // Getters and Setters
    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getCellType() {
        return cellType;
    }

    public void setCellType(String cellType) {
        this.cellType = cellType;
    }

    public Integer getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(Integer rowIndex) {
        this.rowIndex = rowIndex;
    }

    public Integer getColIndex() {
        return colIndex;
    }

    public void setColIndex(Integer colIndex) {
        this.colIndex = colIndex;
    }

    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    public String getCellValue() {
        return cellValue;
    }

    public void setCellValue(String cellValue) {
        this.cellValue = cellValue;
    }

    public String getCellStyle() {
        return cellStyle;
    }

    public void setCellStyle(String cellStyle) {
        this.cellStyle = cellStyle;
    }

    public String getValidation() {
        return validation;
    }

    public void setValidation(String validation) {
        this.validation = validation;
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "ExcelTemplateCell{" +
                "cellId='" + cellId + '\'' +
                ", cellType='" + cellType + '\'' +
                ", rowIndex=" + rowIndex +
                ", colIndex=" + colIndex +
                ", fieldCode='" + fieldCode + '\'' +
                ", cellValue='" + cellValue + '\'' +
                '}';
    }
}
