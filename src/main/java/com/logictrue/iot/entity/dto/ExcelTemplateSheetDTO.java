package com.logictrue.iot.entity.dto;

import com.logictrue.iot.entity.ExcelTemplateCell;
import com.logictrue.iot.entity.ExcelTemplateField;

import java.util.List;
import java.util.ArrayList;

/**
 * Excel模板Sheet数据传输对象
 */
public class ExcelTemplateSheetDTO {
    private String sheetId;
    private String sheetName;
    private Integer sheetIndex;
    private String description;
    private Boolean active;
    private List<ExcelTemplateCell> cells;
    private List<ExcelTemplateField> fields;

    // 默认构造函数
    public ExcelTemplateSheetDTO() {
        this.cells = new ArrayList<>();
        this.fields = new ArrayList<>();
    }

    // 带参数的构造函数
    public ExcelTemplateSheetDTO(String sheetId, String sheetName, Integer sheetIndex) {
        this.sheetId = sheetId;
        this.sheetName = sheetName;
        this.sheetIndex = sheetIndex;
        this.cells = new ArrayList<>();
        this.fields = new ArrayList<>();
    }

    // Getters and Setters
    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public Integer getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(Integer sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public List<ExcelTemplateCell> getCells() {
        return cells;
    }

    public void setCells(List<ExcelTemplateCell> cells) {
        this.cells = cells != null ? cells : new ArrayList<>();
    }

    public List<ExcelTemplateField> getFields() {
        return fields;
    }

    public void setFields(List<ExcelTemplateField> fields) {
        this.fields = fields != null ? fields : new ArrayList<>();
    }

    // 便捷方法
    public void addCell(ExcelTemplateCell cell) {
        if (this.cells == null) {
            this.cells = new ArrayList<>();
        }
        this.cells.add(cell);
    }

    public void addField(ExcelTemplateField field) {
        if (this.fields == null) {
            this.fields = new ArrayList<>();
        }
        this.fields.add(field);
    }

    @Override
    public String toString() {
        return "ExcelTemplateSheetDTO{" +
                "sheetId='" + sheetId + '\'' +
                ", sheetName='" + sheetName + '\'' +
                ", sheetIndex=" + sheetIndex +
                ", description='" + description + '\'' +
                ", active=" + active +
                ", cellsCount=" + (cells != null ? cells.size() : 0) +
                ", fieldsCount=" + (fields != null ? fields.size() : 0) +
                '}';
    }
}
